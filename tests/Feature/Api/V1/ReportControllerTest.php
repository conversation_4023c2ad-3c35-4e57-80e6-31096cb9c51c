<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Constants\ErrorCodes;
use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * Report Controller Test
 *
 * Tests for report endpoints including permission validation and parameter validation
 */
final class ReportControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unrelatedUser;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Role $systemRootRole;
    private Role $systemAdminRole;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        // Create organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $this->systemRootRole = Role::create([
            'name' => 'root',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $this->systemAdminRole = Role::create([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Create organisation roles
        $this->ownerRole = Role::create([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->memberRole = Role::create([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create users
        $this->systemRootUser = User::factory()->create(['name' => 'System Root']);
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->unrelatedUser = User::factory()->create(['name' => 'Unrelated User']);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->assignRole($this->systemRootRole);
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // Authentication Tests
    // ========================================

    public function test_unauthenticated_user_cannot_access_reports(): void
    {
        $response = $this->getJson('/api/v1/reports/sales');

        $response->assertStatus(401);
    }

    // ========================================
    // Parameter Validation Tests
    // ========================================

    public function test_organisation_ids_parameter_is_required(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_organisation_ids_must_be_array(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => 'not-an-array',
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_organisation_ids_must_contain_valid_organisation_ids(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [99999], // Non-existent organisation ID
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids.0']);
    }

    // ========================================
    // Permission Tests - System Users
    // ========================================

    public function test_system_root_can_access_multiple_organisations(): void
    {
        Sanctum::actingAs($this->systemRootUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id, $this->anotherOrganisation->id],
        ]));

        $response->assertStatus(200);
    }

    public function test_system_admin_can_access_multiple_organisations(): void
    {
        Sanctum::actingAs($this->systemAdminUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id, $this->anotherOrganisation->id],
        ]));

        $response->assertStatus(200);
    }

    // ========================================
    // Permission Tests - Organisation Users
    // ========================================

    public function test_organisation_owner_can_access_own_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_member_can_access_own_organisation(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]));

        $response->assertStatus(200);
    }

    public function test_organisation_owner_cannot_access_multiple_organisations(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id, $this->anotherOrganisation->id],
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_organisation_member_cannot_access_multiple_organisations(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id, $this->anotherOrganisation->id],
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_organisation_owner_cannot_access_other_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->anotherOrganisation->id],
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids.0']);
    }

    public function test_organisation_member_cannot_access_other_organisation(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->anotherOrganisation->id],
        ]));

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids.0']);
    }

    public function test_unrelated_user_cannot_access_any_organisation(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->getJson('/api/v1/reports/sales?' . http_build_query([
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]));

        $response->assertStatus(403); // Should be blocked by policy
    }

    // ========================================
    // All Endpoints Tests
    // ========================================

    public function test_all_report_endpoints_require_organisation_ids(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $endpoints = [
            '/api/v1/reports/sales',
            '/api/v1/reports/volume',
            '/api/v1/reports/refunds',
            '/api/v1/reports/order-status',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint . '?' . http_build_query([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
            ]));

            $response->assertStatus(422)
                     ->assertJsonValidationErrors(['organisation_ids']);
        }
    }

    public function test_all_report_endpoints_work_with_valid_organisation_ids(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $endpoints = [
            '/api/v1/reports/sales',
            '/api/v1/reports/volume',
            '/api/v1/reports/refunds',
            '/api/v1/reports/order-status',
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint . '?' . http_build_query([
                'start_date' => '2024-01-01',
                'end_date' => '2024-01-31',
                'organisation_ids' => [$this->organisation->id],
            ]));

            $response->assertStatus(200);
        }
    }

    // ========================================
    // Export Endpoint Tests
    // ========================================

    public function test_export_endpoint_requires_organisation_ids(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_export_endpoint_works_with_valid_organisation_ids(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]);

        $response->assertStatus(200);
    }

    public function test_organisation_member_cannot_export_reports(): void
    {
        Sanctum::actingAs($this->memberUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]);

        $response->assertStatus(403); // Should be blocked by export policy
    }

    public function test_organisation_owner_can_export_reports(): void
    {
        Sanctum::actingAs($this->ownerUser);

        $response = $this->postJson('/api/v1/reports/export', [
            'report_type' => 'sales',
            'format' => 'xlsx',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'organisation_ids' => [$this->organisation->id],
        ]);

        $response->assertStatus(200);
    }
}
