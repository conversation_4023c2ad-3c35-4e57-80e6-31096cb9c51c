<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * Report Service
 * 
 * Main service class for handling report generation and data processing.
 * Implements permission-based data filtering and caching strategies.
 */
final class ReportService
{
    public function __construct(
        private readonly SalesReportService $salesReportService,
        private readonly RefundReportService $refundReportService,
        private readonly OrderStatusReportService $orderStatusReportService
    ) {}

    /**
     * Get sales report data
     */
    public function getSalesReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('sales', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->salesReportService->generateReport($query, $filters);
        });
    }

    /**
     * Get volume report data
     */
    public function getVolumeReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('volume', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->salesReportService->generateVolumeReport($query, $filters);
        });
    }

    /**
     * Get refund report data
     */
    public function getRefundReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('refunds', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->refundReportService->generateReport($query, $filters);
        });
    }

    /**
     * Get order status report data
     */
    public function getOrderStatusReport(array $filters): array
    {
        $cacheKey = $this->generateCacheKey('order_status', $filters);

        return Cache::remember($cacheKey, 3600, function () use ($filters) {
            $query = $this->buildBaseQuery($filters);
            return $this->orderStatusReportService->generateReport($query, $filters);
        });
    }

    /**
     * Export report data
     */
    public function exportReport(array $config): array
    {
        // This will be implemented in later phases
        return [
            'message' => 'Export functionality will be implemented in phase 5',
            'config' => $config,
        ];
    }

    /**
     * Build base query with filters and permissions
     */
    private function buildBaseQuery(array $filters): \Illuminate\Database\Eloquent\Builder
    {
        $query = Order::query()
            ->with(['orderItems'])
            ->whereBetween('completed_at', [$filters['start_date'], $filters['end_date']]);

        // Apply permission filters
        $this->applyPermissionFilters($query);

        // Apply other filters
        if (!empty($filters['countries'])) {
            $query->whereIn('shipping_country', $filters['countries']);
        }

        if (!empty($filters['states'])) {
            $query->whereIn('state', $filters['states']);
        }

        if (!empty($filters['payment_states'])) {
            $query->whereIn('payment_state', $filters['payment_states']);
        }

        if (!empty($filters['organisation_ids'])) {
            // For now, we'll implement a placeholder for organization filtering
            // This will need to be updated when order-organization relationships are established
            $query->where(function ($q) use ($filters) {
                // Placeholder: This will be implemented when order-organization relationship is defined
                $q->whereRaw('1 = 1'); // Allow all for now
            });
        }

        if (isset($filters['include_refunds']) && !$filters['include_refunds']) {
            $query->where(function ($q) {
                $q->whereNull('refund_status')
                  ->orWhere('refund_status', '!=', 'success');
            });
        }

        if (!empty($filters['refund_status'])) {
            $query->where('refund_status', $filters['refund_status']);
        }

        return $query;
    }

    /**
     * Apply permission-based filters
     */
    private function applyPermissionFilters(\Illuminate\Database\Eloquent\Builder $query): void
    {
        $user = auth()->user();

        if (!$user instanceof User) {
            throw new \Exception('User not authenticated');
        }

        // System admins can view all data
        if ($user->hasSystemAdminAccess()) {
            return;
        }

        // For now, organization users can view all data
        // This will be updated when order-organization relationships are established
        $organisationIds = $user->getOrganisationIds();

        if ($organisationIds->isEmpty()) {
            // User not in any organization, return empty results
            $query->whereRaw('1 = 0');
            return;
        }

        // Placeholder for organization-based filtering
        // This will be implemented when order-organization relationship is defined
    }

    /**
     * Generate cache key for report data
     */
    private function generateCacheKey(string $type, array $filters): string
    {
        $user = auth()->user();
        $userContext = $user ? $user->id : 'guest';

        return sprintf(
            'reports:%s:%s:%s',
            $type,
            $userContext,
            md5(serialize($filters))
        );
    }
}
