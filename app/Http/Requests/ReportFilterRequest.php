<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Constants\ErrorCodes;
use App\Models\Organisation;

/**
 * Report Filter Request
 *
 * Validates report filtering parameters including date ranges, grouping options,
 * country filters, and organization filters.
 */
final class ReportFilterRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return [
            'start_date' => ['required', 'date', 'before_or_equal:end_date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date', 'before_or_equal:today'],
            'group_by' => ['sometimes', 'string', 'in:day,week,month,quarter,year'],
            'countries' => ['sometimes', 'array'],
            'countries.*' => ['string', 'size:2'], // ISO 2-letter country codes
            'states' => ['sometimes', 'array'],
            'states.*' => ['string', 'in:completed,cancelled,processing,pending'],
            'payment_states' => ['sometimes', 'array'],
            'payment_states.*' => ['string', 'in:completed,pending,failed,cancelled'],
            'organisation_ids' => ['required', 'array', 'min:1'],
            'organisation_ids.*' => ['required', 'integer', 'exists:organisations,id'],
            'currency' => ['sometimes', 'string', 'size:3'], // ISO currency code
            'timezone' => ['sometimes', 'string', 'timezone'],
            'include_refunds' => ['sometimes', 'boolean'],
            'refund_status' => ['sometimes', 'string', 'in:success,pending,failed'],
        ];
    }

    /**
     * Get custom messages for validator errors
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'start_date.required' => __('validation.reports.start_date_required'),
            'end_date.required' => __('validation.reports.end_date_required'),
            'start_date.before_or_equal' => __('validation.reports.invalid_date_range'),
            'end_date.before_or_equal' => __('validation.reports.end_date_future'),
            'countries.*.size' => __('validation.reports.invalid_country_code'),
            'group_by.in' => __('validation.reports.invalid_group_by'),
            'states.*.in' => __('validation.reports.invalid_state'),
            'payment_states.*.in' => __('validation.reports.invalid_payment_state'),
            'organisation_ids.required' => __('validation.reports.organisation_ids_required'),
            'organisation_ids.array' => __('validation.reports.organisation_ids_must_be_array'),
            'organisation_ids.min' => __('validation.reports.organisation_ids_min_one'),
            'organisation_ids.*.exists' => __('validation.reports.invalid_organisation'),
            'currency.size' => __('validation.reports.invalid_currency_code'),
            'timezone.timezone' => __('validation.reports.invalid_timezone'),
        ]);
    }

    /**
     * Get custom attribute names for validator errors
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'start_date' => __('validation.attributes.start_date'),
            'end_date' => __('validation.attributes.end_date'),
            'group_by' => __('validation.attributes.group_by'),
            'countries' => __('validation.attributes.countries'),
            'states' => __('validation.attributes.states'),
            'payment_states' => __('validation.attributes.payment_states'),
            'organisation_ids' => __('validation.attributes.organisation_ids'),
            'currency' => __('validation.attributes.currency'),
            'timezone' => __('validation.attributes.timezone'),
            'include_refunds' => __('validation.attributes.include_refunds'),
            'refund_status' => __('validation.attributes.refund_status'),
        ]);
    }

    /**
     * Configure the validator instance
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateOrganisationAccess($validator);
        });
    }

    /**
     * Validate organisation access permissions
     */
    protected function validateOrganisationAccess($validator): void
    {
        $user = $this->user();
        $organisationIds = $this->input('organisation_ids', []);

        // Skip validation if no organisation_ids provided (will be caught by required rule)
        if (empty($organisationIds)) {
            return;
        }

        // System admins can access any organisation
        if ($user && $user->hasSystemAdminAccess()) {
            return;
        }

        // Non-system users can only specify one organisation
        if (count($organisationIds) > 1) {
            $validator->errors()->add(
                'organisation_ids',
                __('errors.report_multiple_organisations_not_allowed')
            );
            return;
        }

        // Check if user belongs to the specified organisation
        $organisationId = $organisationIds[0];
        if ($user && !$user->belongsToOrganisation($organisationId)) {
            $validator->errors()->add(
                'organisation_ids.0',
                __('errors.report_organisation_access_denied')
            );
        }
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        // Set default timezone if not provided
        if (!$this->has('timezone')) {
            $this->merge(['timezone' => config('app.timezone')]);
        }

        // Set default group_by if not provided
        if (!$this->has('group_by')) {
            $this->merge(['group_by' => 'day']);
        }

        // Set default currency if not provided
        if (!$this->has('currency')) {
            $this->merge(['currency' => 'USD']);
        }

        // Convert include_refunds to boolean if provided as string
        if ($this->has('include_refunds') && is_string($this->include_refunds)) {
            $this->merge(['include_refunds' => filter_var($this->include_refunds, FILTER_VALIDATE_BOOLEAN)]);
        }
    }

    /**
     * Get the validated data with processed values
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();

        // Ensure dates are in proper format
        $data['start_date'] = $this->date('start_date')->startOfDay()->toDateTimeString();
        $data['end_date'] = $this->date('end_date')->endOfDay()->toDateTimeString();

        return $data;
    }
}
