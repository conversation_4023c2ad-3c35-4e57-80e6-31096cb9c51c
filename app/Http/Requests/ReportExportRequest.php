<?php

declare(strict_types=1);

namespace App\Http\Requests;

/**
 * Report Export Request
 * 
 * Validates report export parameters including format, report type,
 * and filtering options.
 */
final class ReportExportRequest extends ReportFilterRequest
{
    /**
     * Get the validation rules that apply to the request
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'report_type' => ['required', 'string', 'in:sales,volume,refunds,order_status'],
            'format' => ['required', 'string', 'in:xlsx,csv,pdf'],
            'filename' => ['sometimes', 'string', 'max:255', 'regex:/^[a-zA-Z0-9_\-\s]+$/'],
            'include_summary' => ['sometimes', 'boolean'],
            'include_charts' => ['sometimes', 'boolean'],
            'max_records' => ['sometimes', 'integer', 'min:1', 'max:50000'],
            'email_to' => ['sometimes', 'email'],
            'async' => ['sometimes', 'boolean'],
        ]);
    }

    /**
     * Get custom messages for validator errors
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'report_type.required' => __('validation.reports.report_type_required'),
            'report_type.in' => __('validation.reports.invalid_report_type'),
            'format.required' => __('validation.reports.format_required'),
            'format.in' => __('validation.reports.invalid_format'),
            'filename.regex' => __('validation.reports.invalid_filename'),
            'filename.max' => __('validation.reports.filename_too_long'),
            'max_records.max' => __('validation.reports.max_records_exceeded'),
            'max_records.min' => __('validation.reports.min_records_required'),
            'email_to.email' => __('validation.reports.invalid_email'),
        ]);
    }

    /**
     * Get custom attribute names for validator errors
     */
    public function attributes(): array
    {
        return array_merge(parent::attributes(), [
            'report_type' => __('validation.attributes.report_type'),
            'format' => __('validation.attributes.format'),
            'filename' => __('validation.attributes.filename'),
            'include_summary' => __('validation.attributes.include_summary'),
            'include_charts' => __('validation.attributes.include_charts'),
            'max_records' => __('validation.attributes.max_records'),
            'email_to' => __('validation.attributes.email_to'),
            'async' => __('validation.attributes.async'),
        ]);
    }

    /**
     * Prepare the data for validation
     */
    protected function prepareForValidation(): void
    {
        parent::prepareForValidation();

        // Set default format if not provided
        if (!$this->has('format')) {
            $this->merge(['format' => 'xlsx']);
        }

        // Set default max_records if not provided
        if (!$this->has('max_records')) {
            $this->merge(['max_records' => 10000]);
        }

        // Set default filename if not provided
        if (!$this->has('filename')) {
            $reportType = $this->input('report_type', 'report');
            $timestamp = now()->format('Y-m-d_H-i-s');
            $this->merge(['filename' => "{$reportType}_report_{$timestamp}"]);
        }

        // Convert boolean fields
        foreach (['include_summary', 'include_charts', 'async'] as $field) {
            if ($this->has($field) && is_string($this->input($field))) {
                $this->merge([$field => filter_var($this->input($field), FILTER_VALIDATE_BOOLEAN)]);
            }
        }

        // Set defaults for boolean fields
        if (!$this->has('include_summary')) {
            $this->merge(['include_summary' => true]);
        }

        if (!$this->has('include_charts')) {
            $this->merge(['include_charts' => false]);
        }

        if (!$this->has('async')) {
            // Use async for large exports
            $maxRecords = $this->input('max_records', 10000);
            $this->merge(['async' => $maxRecords > 5000]);
        }
    }

    /**
     * Determine if the export should be processed asynchronously
     */
    public function shouldProcessAsync(): bool
    {
        return $this->boolean('async') || $this->integer('max_records') > 5000;
    }

    /**
     * Get the export configuration
     */
    public function getExportConfig(): array
    {
        return [
            'report_type' => $this->string('report_type'),
            'format' => $this->string('format'),
            'filename' => $this->string('filename'),
            'include_summary' => $this->boolean('include_summary'),
            'include_charts' => $this->boolean('include_charts'),
            'max_records' => $this->integer('max_records'),
            'email_to' => $this->string('email_to'),
            'async' => $this->shouldProcessAsync(),
        ];
    }
}
