<?php

declare(strict_types=1);

namespace App\Http\Resources\Api\V1;

use App\Http\Resources\ApiResource;
use Illuminate\Http\Request;

/**
 * Refund Report Resource
 * 
 * Transforms refund report data for API responses with chart-compatible format
 */
final class RefundReportResource extends ApiResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $data = $this->resource;
        
        $transformed = [];

        // Transform chart_data for refund trend charts
        if (isset($data['chart_data']) && is_array($data['chart_data'])) {
            $transformed['refund_trend_chart'] = $this->transformRefundTrendChart($data['chart_data']);
        }

        // Transform reasons_data for refund reasons chart
        if (isset($data['reasons_data']) && is_array($data['reasons_data'])) {
            $transformed['refund_reasons_chart'] = $this->transformRefundReasonsChart($data['reasons_data']);
        }

        return $transformed;
    }

    /**
     * Transform refund trend data for line chart
     */
    private function transformRefundTrendChart(array $chartData): array
    {
        $dates = [];
        $refundAmounts = [];
        $refundCounts = [];

        foreach ($chartData as $item) {
            // Convert date format from YYYY-MM-DD to MM/DD
            $date = date('m/d', strtotime($item['period']));
            $dates[] = $date;

            // Convert amount from cents to dollars
            $refundAmounts[] = (int) ($item['total_refunds'] / 100);
            $refundCounts[] = (int) $item['refunded_orders'];
        }

        return [
            'xAxis' => [
                'data' => $dates
            ],
            'series' => [
                [
                    'name' => __('api.reports.chart_labels.refund_amount'),
                    'data' => $refundAmounts
                ],
                [
                    'name' => __('api.reports.chart_labels.refund_orders'),
                    'data' => $refundCounts
                ]
            ]
        ];
    }

    /**
     * Transform refund reasons data for pie chart
     */
    private function transformRefundReasonsChart(array $reasonsData): array
    {
        $data = [];

        foreach ($reasonsData as $item) {
            $data[] = [
                'name' => $item['reason'] ?: __('api.reports.chart_labels.no_reason_provided'),
                'value' => (int) $item['refund_count']
            ];
        }

        return [
            'series' => [
                [
                    'data' => $data
                ]
            ]
        ];
    }
}
